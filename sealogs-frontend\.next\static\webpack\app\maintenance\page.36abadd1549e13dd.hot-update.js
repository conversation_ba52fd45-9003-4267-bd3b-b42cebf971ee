"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   createAssignedColumn: function() { return /* binding */ createAssignedColumn; },\n/* harmony export */   createInventoryColumn: function() { return /* binding */ createInventoryColumn; },\n/* harmony export */   createLocationColumn: function() { return /* binding */ createLocationColumn; },\n/* harmony export */   createMaintenanceColumns: function() { return /* binding */ createMaintenanceColumns; },\n/* harmony export */   createSortingFunction: function() { return /* binding */ createSortingFunction; },\n/* harmony export */   createStatusColumn: function() { return /* binding */ createStatusColumn; },\n/* harmony export */   createTitleColumn: function() { return /* binding */ createTitleColumn; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,createSortingFunction,createTitleColumn,createLocationColumn,createAssignedColumn,createInventoryColumn,createStatusColumn,createMaintenanceColumns,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Column utility functions for reusable table column patterns\nconst createSortingFunction = (fieldPath)=>{\n    return (rowA, rowB)=>{\n        const getNestedValue = (obj, path)=>{\n            return path.split(\".\").reduce((current, key)=>current === null || current === void 0 ? void 0 : current[key], obj) || \"\";\n        };\n        const valueA = getNestedValue(rowA.original, fieldPath);\n        const valueB = getNestedValue(rowB.original, fieldPath);\n        return valueA.localeCompare(valueB);\n    };\n};\nconst createTitleColumn = (options)=>({\n        accessorKey: \"title\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Title\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2, _maintenanceCheck_assignedTo;\n            const maintenanceCheck = row.original;\n            const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n            const getStatusColorClasses = (status)=>{\n                switch(status){\n                    case \"High\":\n                        return \"text-cinnabar-500 hover:text-distructive/60\";\n                    case \"Upcoming\":\n                        return \"text-fire-bush-500 hover:text-distructive/60\";\n                    default:\n                        return \"hover:text-curious-blue-400\";\n                }\n            };\n            const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, options.vessels || []);\n            const vesselWithIcon = options.getVesselWithIcon ? options.getVesselWithIcon((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id, vesselDetails) : vesselDetails;\n            var _maintenanceCheck_name;\n            const taskLink = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(options.pathname, \"?\").concat(options.searchParams.toString()),\n                className: getStatusColorClasses(overDueStatus),\n                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, undefined);\n            if (options.enhanced) {\n                var _maintenanceCheck_assignedTo1, _maintenanceCheck_assignedTo2, _maintenanceCheck_inventory, _maintenanceCheck_basicComponent3;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) || 0, options.crewInfo);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                            className: \"xs:hidden p-2.5 w-full shadow-none rounded-none bg-transparent\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                                className: \"p-0 flex gap-1.5\",\n                                children: [\n                                    ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                        variant: \"secondary\",\n                                        className: \"h-8 w-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                            className: \"text-xs\",\n                                            children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            taskLink,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                className: \"hover:underline text-sm\",\n                                                children: maintenanceCheck.inventory.item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden xs:block\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center flex-nowrap gap-2\",\n                                    children: taskLink\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: ((_maintenanceCheck_basicComponent3 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent3 === void 0 ? void 0 : _maintenanceCheck_basicComponent3.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            options.enhanced && options.getVesselWithIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"size-5 flex items-center justify-center flex-shrink-0 [&_img]:!size-5 [&_svg]:!size-5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                vessel: vesselWithIcon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                        children: maintenanceCheck.basicComponent.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 45\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_21__.cn)(\"text-base\"),\n                                                children: maintenanceCheck.basicComponent.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            }\n            // Simple version for MaintenanceTable\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: taskLink\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 17\n                    }, undefined),\n                    options.showMobileLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden\",\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                            className: \"text-sm text-muted-foreground hover:underline\",\n                            children: maintenanceCheck.basicComponent.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 21\n                    }, undefined),\n                    options.showMobileAssigned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden space-y-2\",\n                        children: ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"Assigned to:\",\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 209,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"name\")\n    });\nconst createLocationColumn = (options)=>({\n        accessorKey: \"location\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Location\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, undefined);\n        },\n        cellAlignment: options.cellAlignment || \"left\",\n        breakpoint: options.breakpoint,\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n            const maintenanceCheck = row.original;\n            const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, options.vessels);\n            if (options.enhanced && options.getVesselWithIcon) {\n                var _maintenanceCheck_basicComponent2, _maintenanceCheck_basicComponent3;\n                // Enhanced version with VesselIcon\n                const vesselWithIcon = options.getVesselWithIcon(((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) || 0, vesselDetails);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: ((_maintenanceCheck_basicComponent3 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent3 === void 0 ? void 0 : _maintenanceCheck_basicComponent3.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-fit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                vessel: vesselWithIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                        children: maintenanceCheck.basicComponent.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false);\n            }\n            // Simple version with Avatar\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                            size: \"sm\",\n                            variant: \"secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                className: \"text-xs\",\n                                children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                            className: \"hover:underline\",\n                            children: maintenanceCheck.basicComponent.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 312,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"basicComponent.title\")\n    });\nconst createAssignedColumn = (options)=>({\n        accessorKey: \"assigned\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Assigned to\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, undefined);\n        },\n        cellAlignment: options.cellAlignment || \"left\",\n        breakpoint: options.breakpoint,\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const maintenanceCheck = row.original;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, options.crewInfo);\n            const wrapperClass = options.hideOnMobile ? \"hidden md:block\" : \"\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: wrapperClass,\n                children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                            variant: \"secondary\",\n                            className: \"h-8 w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                className: \"text-xs\",\n                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                            className: \"hover:underline hidden lg:block\",\n                            children: maintenanceCheck.assignedTo.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 369,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"assignedTo.name\")\n    });\nconst createInventoryColumn = (options)=>({\n        accessorKey: \"inventory\",\n        header: \"Inventory item\",\n        cellAlignment: options.cellAlignment || \"left\",\n        breakpoint: options.breakpoint,\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_inventory;\n            const maintenanceCheck = row.original;\n            const wrapperClass = options.hideOnMobile ? \"hidden md:block\" : \"\";\n            if (options.enhanced) {\n                var _maintenanceCheck_inventory1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: wrapperClass,\n                    children: ((_maintenanceCheck_inventory1 = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory1 === void 0 ? void 0 : _maintenanceCheck_inventory1.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 17\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: wrapperClass,\n                children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                    className: \"hover:underline\",\n                    children: maintenanceCheck.inventory.item\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 434,\n                columnNumber: 13\n            }, undefined);\n        }\n    });\nconst createStatusColumn = (options)=>({\n        accessorKey: \"status\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 455,\n                columnNumber: 9\n            }, undefined);\n        },\n        cellAlignment: options.cellAlignment || \"right\",\n        cell: (param)=>{\n            let { row } = param;\n            const maintenanceCheck = row.original;\n            const wrapperClass = options.hideOnMobile ? \"hidden md:block\" : \"\";\n            if (options.enhanced && options.bp) {\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 24\n                    }, undefined);\n                }\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: wrapperClass,\n                    children: overDueStatus === \"High\" ? !options.bp[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" items-end mr-2.5 text-nowrap w-fit\\n                                            \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\", \"\\n                                            \"),\n                        children: overDueDays * -1 + \" days ago\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 17\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: wrapperClass,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                    maintenanceCheck: maintenanceCheck\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 491,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"isOverDue.days\")\n    });\n// Consolidated createMaintenanceColumns function for both simple and enhanced tables\nconst createMaintenanceColumns = function(crewInfo, getVesselWithIcon, pathname, searchParams) {\n    let options = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n    const { enhanced = true, showVessel = false, vessels: optionVessels, bp: optionBp } = options;\n    const finalVessels = optionVessels || [];\n    const finalBp = optionBp;\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        createTitleColumn({\n            pathname,\n            searchParams,\n            enhanced,\n            crewInfo,\n            showMobileLocation: !showVessel,\n            showMobileAssigned: !enhanced,\n            vessels: finalVessels,\n            getVesselWithIcon: enhanced ? getVesselWithIcon : undefined\n        }),\n        ...showVessel ? [] : [\n            createLocationColumn({\n                vessels: finalVessels,\n                getVesselWithIcon: enhanced ? getVesselWithIcon : undefined,\n                enhanced,\n                cellAlignment: \"left\",\n                breakpoint: enhanced ? \"laptop\" : undefined\n            })\n        ],\n        createAssignedColumn({\n            crewInfo,\n            cellAlignment: \"left\",\n            breakpoint: enhanced ? \"tablet-md\" : \"landscape\",\n            hideOnMobile: !enhanced\n        }),\n        createInventoryColumn({\n            cellAlignment: \"left\",\n            breakpoint: enhanced ? \"phablet\" : \"tablet-lg\",\n            enhanced,\n            hideOnMobile: !enhanced\n        }),\n        createStatusColumn({\n            cellAlignment: \"right\",\n            enhanced,\n            bp: finalBp,\n            hideOnMobile: !enhanced\n        })\n    ]);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 671,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base mr-2.5 !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 678,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    const columns = createMaintenanceColumns(crewInfo, getVesselWithIcon, pathname, searchParams, {\n        enhanced: false,\n        showVessel,\n        vessels\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 713,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"BtAwTu7MooJrudiBzqv5a/psZQo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__.useBreakpoints)();\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_19__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon, pathname, searchParams, {\n        enhanced: true,\n        showVessel: false,\n        vessels,\n        bp\n    });\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_11__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_18__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1062,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_10__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1064,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1059,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1069,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1077,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1067,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"3fKdCNw0/HXDN4h5aZ/KGPO1O24=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});