"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx":
/*!**********************************************!*\
  !*** ./src/app/ui/maintenance/list/list.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceTable: function() { return /* binding */ MaintenanceTable; },\n/* harmony export */   StatusBadge: function() { return /* binding */ StatusBadge; },\n/* harmony export */   createAssignedColumn: function() { return /* binding */ createAssignedColumn; },\n/* harmony export */   createInventoryColumn: function() { return /* binding */ createInventoryColumn; },\n/* harmony export */   createLocationColumn: function() { return /* binding */ createLocationColumn; },\n/* harmony export */   createMaintenanceColumns: function() { return /* binding */ createMaintenanceColumns; },\n/* harmony export */   createSortingFunction: function() { return /* binding */ createSortingFunction; },\n/* harmony export */   createStatusColumn: function() { return /* binding */ createStatusColumn; },\n/* harmony export */   createTitleColumn: function() { return /* binding */ createTitleColumn; },\n/* harmony export */   \"default\": function() { return /* binding */ TaskList; },\n/* harmony export */   getVesselDetails: function() { return /* binding */ getVesselDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filter/components/maintenance-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/maintenance/list/queries.ts\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ getVesselDetails,createSortingFunction,createTitleColumn,createLocationColumn,createAssignedColumn,createInventoryColumn,createStatusColumn,createMaintenanceColumns,StatusBadge,MaintenanceTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions\nconst getCrewInitials = (firstName, surname)=>{\n    var _firstName_charAt, _surname_charAt;\n    if (!firstName && !surname) return \"??\";\n    const first = (firstName === null || firstName === void 0 ? void 0 : (_firstName_charAt = firstName.charAt(0)) === null || _firstName_charAt === void 0 ? void 0 : _firstName_charAt.toUpperCase()) || \"\";\n    const last = (surname === null || surname === void 0 ? void 0 : (_surname_charAt = surname.charAt(0)) === null || _surname_charAt === void 0 ? void 0 : _surname_charAt.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\nconst getVesselInitials = (title)=>{\n    if (!title) return \"??\";\n    const words = title.split(\" \").filter((word)=>word.length > 0);\n    if (words.length === 1) {\n        return words[0].substring(0, 2).toUpperCase();\n    }\n    return words.slice(0, 2).map((word)=>word.charAt(0).toUpperCase()).join(\"\");\n};\nconst getCrewDetails = (assignedToID, crewInfo)=>{\n    return crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.find((crew)=>crew.id === assignedToID.toString());\n};\nconst getVesselDetails = (vesselID, vessels)=>{\n    return vessels === null || vessels === void 0 ? void 0 : vessels.find((vessel)=>vessel.id === vesselID);\n};\n// Column utility functions for reusable table column patterns\nconst createSortingFunction = (fieldPath)=>{\n    return (rowA, rowB)=>{\n        const getNestedValue = (obj, path)=>{\n            return path.split(\".\").reduce((current, key)=>current === null || current === void 0 ? void 0 : current[key], obj) || \"\";\n        };\n        const valueA = getNestedValue(rowA.original, fieldPath);\n        const valueB = getNestedValue(rowB.original, fieldPath);\n        return valueA.localeCompare(valueB);\n    };\n};\nconst createTitleColumn = (options)=>({\n        accessorKey: \"title\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Title\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_isOverDue, _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1, _maintenanceCheck_basicComponent2, _maintenanceCheck_assignedTo;\n            const maintenanceCheck = row.original;\n            const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n            const getStatusColorClasses = (status)=>{\n                switch(status){\n                    case \"High\":\n                        return \"text-distructive/60 hover:text-distructive/60\";\n                    case \"Upcoming\":\n                        return \"text-fire-bush-500 hover:text-warning/60\";\n                    default:\n                        return \"hover:text-curious-blue-400\";\n                }\n            };\n            const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, options.vessels || []);\n            const vesselWithIcon = options.getVesselWithIcon ? options.getVesselWithIcon((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id, vesselDetails) : vesselDetails;\n            var _maintenanceCheck_name;\n            const taskLink = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                href: \"/maintenance?taskID=\".concat(maintenanceCheck.id, \"&redirect_to=\").concat(options.pathname, \"?\").concat(options.searchParams.toString()),\n                className: getStatusColorClasses(overDueStatus),\n                children: (_maintenanceCheck_name = maintenanceCheck.name) !== null && _maintenanceCheck_name !== void 0 ? _maintenanceCheck_name : \"Task #\".concat(maintenanceCheck.id, \" (No Name) - \").concat(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(maintenanceCheck.created).format(\"DD/MM/YYYY\"))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 118,\n                columnNumber: 13\n            }, undefined);\n            if (options.enhanced) {\n                var _maintenanceCheck_assignedTo1, _maintenanceCheck_assignedTo2, _maintenanceCheck_inventory, _maintenanceCheck_basicComponent3;\n                const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) || 0, options.crewInfo);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.Card, {\n                            className: \"xs:hidden p-2.5 w-full shadow-none rounded-none bg-transparent\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_17__.CardContent, {\n                                className: \"p-0 flex gap-1.5\",\n                                children: [\n                                    ((_maintenanceCheck_assignedTo2 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo2 === void 0 ? void 0 : _maintenanceCheck_assignedTo2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                        variant: \"secondary\",\n                                        className: \"h-8 w-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                            className: \"text-xs\",\n                                            children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            taskLink,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                                                className: \"hover:underline text-sm\",\n                                                children: maintenanceCheck.inventory.item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden xs:block\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center flex-nowrap gap-2\",\n                                    children: taskLink\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: ((_maintenanceCheck_basicComponent3 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent3 === void 0 ? void 0 : _maintenanceCheck_basicComponent3.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            options.enhanced && options.getVesselWithIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"size-5 flex items-center justify-center flex-shrink-0 [&_img]:!size-5 [&_svg]:!size-5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                vessel: vesselWithIcon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                        children: maintenanceCheck.basicComponent.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 45\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_21__.cn)(\"text-base\"),\n                                                children: maintenanceCheck.basicComponent.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            }\n            // Simple version for MaintenanceTable\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: taskLink\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 17\n                    }, undefined),\n                    options.showMobileLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden\",\n                        children: ((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                            className: \"text-sm text-muted-foreground hover:underline\",\n                            children: maintenanceCheck.basicComponent.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 21\n                    }, undefined),\n                    options.showMobileAssigned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden space-y-2\",\n                        children: ((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"Assigned to:\",\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                                    className: \"hover:underline\",\n                                    children: maintenanceCheck.assignedTo.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 209,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"name\")\n    });\nconst createLocationColumn = (options)=>({\n        accessorKey: \"location\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Location\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, undefined);\n        },\n        cellAlignment: options.cellAlignment || \"left\",\n        breakpoint: options.breakpoint,\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_basicComponent, _maintenanceCheck_basicComponent1;\n            const maintenanceCheck = row.original;\n            const vesselDetails = getVesselDetails(((_maintenanceCheck_basicComponent = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent === void 0 ? void 0 : _maintenanceCheck_basicComponent.id) || 0, options.vessels);\n            if (options.enhanced && options.getVesselWithIcon) {\n                var _maintenanceCheck_basicComponent2, _maintenanceCheck_basicComponent3;\n                // Enhanced version with VesselIcon\n                const vesselWithIcon = options.getVesselWithIcon(((_maintenanceCheck_basicComponent2 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent2 === void 0 ? void 0 : _maintenanceCheck_basicComponent2.id) || 0, vesselDetails);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: ((_maintenanceCheck_basicComponent3 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent3 === void 0 ? void 0 : _maintenanceCheck_basicComponent3.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-fit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                vessel: vesselWithIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                        children: maintenanceCheck.basicComponent.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                                className: \"hover:underline\",\n                                children: maintenanceCheck.basicComponent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false);\n            }\n            // Simple version with Avatar\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: ((_maintenanceCheck_basicComponent1 = maintenanceCheck.basicComponent) === null || _maintenanceCheck_basicComponent1 === void 0 ? void 0 : _maintenanceCheck_basicComponent1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                            size: \"sm\",\n                            variant: \"secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                className: \"text-xs\",\n                                children: getVesselInitials((vesselDetails === null || vesselDetails === void 0 ? void 0 : vesselDetails.title) || maintenanceCheck.basicComponent.title || undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/vessel/info?id=\".concat(maintenanceCheck.basicComponent.id),\n                            className: \"hover:underline\",\n                            children: maintenanceCheck.basicComponent.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 312,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"basicComponent.title\")\n    });\nconst createAssignedColumn = (options)=>({\n        accessorKey: \"assigned\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Assigned to\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, undefined);\n        },\n        cellAlignment: options.cellAlignment || \"left\",\n        breakpoint: options.breakpoint,\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_assignedTo, _maintenanceCheck_assignedTo1;\n            const maintenanceCheck = row.original;\n            const crewDetails = getCrewDetails(((_maintenanceCheck_assignedTo = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo === void 0 ? void 0 : _maintenanceCheck_assignedTo.id) || 0, options.crewInfo);\n            const wrapperClass = options.hideOnMobile ? \"hidden md:block\" : \"\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: wrapperClass,\n                children: ((_maintenanceCheck_assignedTo1 = maintenanceCheck.assignedTo) === null || _maintenanceCheck_assignedTo1 === void 0 ? void 0 : _maintenanceCheck_assignedTo1.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                            variant: \"secondary\",\n                            className: \"h-8 w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                className: \"text-xs\",\n                                children: getCrewInitials(crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.firstName, crewDetails === null || crewDetails === void 0 ? void 0 : crewDetails.surname)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/crew/info?id=\".concat(maintenanceCheck.assignedTo.id),\n                            className: \"hover:underline hidden lg:block\",\n                            children: maintenanceCheck.assignedTo.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 369,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"assignedTo.name\")\n    });\nconst createInventoryColumn = (options)=>({\n        accessorKey: \"inventory\",\n        header: \"Inventory item\",\n        cellAlignment: options.cellAlignment || \"left\",\n        breakpoint: options.breakpoint,\n        cell: (param)=>{\n            let { row } = param;\n            var _maintenanceCheck_inventory;\n            const maintenanceCheck = row.original;\n            const wrapperClass = options.hideOnMobile ? \"hidden md:block\" : \"\";\n            if (options.enhanced) {\n                var _maintenanceCheck_inventory1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: wrapperClass,\n                    children: ((_maintenanceCheck_inventory1 = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory1 === void 0 ? void 0 : _maintenanceCheck_inventory1.id) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                        className: \"hover:underline\",\n                        children: maintenanceCheck.inventory.item\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 17\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: wrapperClass,\n                children: ((_maintenanceCheck_inventory = maintenanceCheck.inventory) === null || _maintenanceCheck_inventory === void 0 ? void 0 : _maintenanceCheck_inventory.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    href: \"/inventory/view?id=\".concat(maintenanceCheck.inventory.id),\n                    className: \"hover:underline\",\n                    children: maintenanceCheck.inventory.item\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 434,\n                columnNumber: 13\n            }, undefined);\n        }\n    });\nconst createStatusColumn = (options)=>({\n        accessorKey: \"status\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                column: column,\n                title: \"Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 455,\n                columnNumber: 9\n            }, undefined);\n        },\n        cellAlignment: options.cellAlignment || \"right\",\n        cell: (param)=>{\n            let { row } = param;\n            const maintenanceCheck = row.original;\n            const wrapperClass = options.hideOnMobile ? \"hidden md:block\" : \"\";\n            if (options.enhanced && options.bp) {\n                var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1;\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 24\n                    }, undefined);\n                }\n                const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n                const overDueDays = (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.day;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: wrapperClass,\n                    children: overDueStatus === \"High\" ? !options.bp[\"tablet-lg\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" items-end mr-2.5 text-nowrap w-fit\\n                                            \".concat(overDueStatus === \"High\" ? \"alert text-sm !px-1.5 !py-0.5 xs:px-3 xs:py-1 whitespace-nowrap\" : \"\", \"\\n                                            \"),\n                        children: overDueDays * -1 + \" days ago\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 17\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: wrapperClass,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusBadge, {\n                    maintenanceCheck: maintenanceCheck\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 491,\n                columnNumber: 13\n            }, undefined);\n        },\n        sortingFn: createSortingFunction(\"isOverDue.days\")\n    });\n// Consolidated createMaintenanceColumns function for both simple and enhanced tables\nconst createMaintenanceColumns = function(crewInfo, getVesselWithIcon, pathname, searchParams) {\n    let options = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : {};\n    const { enhanced = true, showVessel = false, vessels: optionVessels, bp: optionBp } = options;\n    const finalVessels = optionVessels || [];\n    const finalBp = optionBp;\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        createTitleColumn({\n            pathname,\n            searchParams,\n            enhanced,\n            crewInfo,\n            showMobileLocation: !showVessel,\n            showMobileAssigned: !enhanced,\n            vessels: finalVessels,\n            getVesselWithIcon: enhanced ? getVesselWithIcon : undefined\n        }),\n        ...showVessel ? [] : [\n            createLocationColumn({\n                vessels: finalVessels,\n                getVesselWithIcon: enhanced ? getVesselWithIcon : undefined,\n                enhanced,\n                cellAlignment: \"left\",\n                breakpoint: enhanced ? \"laptop\" : undefined\n            })\n        ],\n        createAssignedColumn({\n            crewInfo,\n            cellAlignment: \"left\",\n            breakpoint: enhanced ? \"tablet-md\" : \"landscape\",\n            hideOnMobile: !enhanced\n        }),\n        createInventoryColumn({\n            cellAlignment: \"left\",\n            breakpoint: enhanced ? \"phablet\" : \"tablet-lg\",\n            enhanced,\n            hideOnMobile: !enhanced\n        }),\n        createStatusColumn({\n            cellAlignment: \"right\",\n            enhanced,\n            bp: finalBp,\n            hideOnMobile: !enhanced\n        })\n    ]);\n};\n// Status badge component following UI standards\nconst StatusBadge = (param)=>{\n    let { maintenanceCheck } = param;\n    var _maintenanceCheck_isOverDue, _maintenanceCheck_isOverDue1, _maintenanceCheck_isOverDue2, _maintenanceCheck_isOverDue3, _maintenanceCheck_isOverDue4, _maintenanceCheck_isOverDue5, _maintenanceCheck_isOverDue6, _maintenanceCheck_isOverDue7, _maintenanceCheck_isOverDue8, _maintenanceCheck_isOverDue9;\n    const isOverdue = (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status) === \"High\";\n    // Get status text\n    let statusText = \"\";\n    if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue1 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue1 === void 0 ? void 0 : _maintenanceCheck_isOverDue1.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(maintenanceCheck.isOverDue.status)) {\n        var _maintenanceCheck_isOverDue10;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue10 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue10 === void 0 ? void 0 : _maintenanceCheck_isOverDue10.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue2 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue2 === void 0 ? void 0 : _maintenanceCheck_isOverDue2.status) === \"Completed\" && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue3 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue3 === void 0 ? void 0 : _maintenanceCheck_isOverDue3.days) === \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue11;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue11 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue11 === void 0 ? void 0 : _maintenanceCheck_isOverDue11.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue4 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue4 === void 0 ? void 0 : _maintenanceCheck_isOverDue4.status) === \"Upcoming\") {\n        var _maintenanceCheck_isOverDue12;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue12 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue12 === void 0 ? void 0 : _maintenanceCheck_isOverDue12.days;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue5 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue5 === void 0 ? void 0 : _maintenanceCheck_isOverDue5.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue6 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue6 === void 0 ? void 0 : _maintenanceCheck_isOverDue6.days)) {\n        var _maintenanceCheck_isOverDue13;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue13 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue13 === void 0 ? void 0 : _maintenanceCheck_isOverDue13.status;\n    } else if ((maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue7 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue7 === void 0 ? void 0 : _maintenanceCheck_isOverDue7.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue8 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue8 === void 0 ? void 0 : _maintenanceCheck_isOverDue8.days) && (maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue9 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue9 === void 0 ? void 0 : _maintenanceCheck_isOverDue9.days) !== \"Save As Draft\") {\n        var _maintenanceCheck_isOverDue14;\n        statusText = maintenanceCheck === null || maintenanceCheck === void 0 ? void 0 : (_maintenanceCheck_isOverDue14 = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue14 === void 0 ? void 0 : _maintenanceCheck_isOverDue14.days;\n    }\n    // Only apply styling to overdue items, others are plain text\n    if (isOverdue) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"alert w-fit inline-block rounded-md text-nowrap mr-2.5 text-sm xs:text-base !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n            children: statusText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n            lineNumber: 671,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-nowrap text-sm xs:text-base mr-2.5 !px-1.5 !py-0.5 xs:px-3 xs:py-1\",\n        children: statusText\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 678,\n        columnNumber: 9\n    }, undefined);\n};\n_c = StatusBadge;\n// Reusable MaintenanceTable component that accepts props\nfunction MaintenanceTable(param) {\n    let { maintenanceChecks, vessels, crewInfo, showVessel = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    const columns = createMaintenanceColumns(crewInfo, getVesselWithIcon, pathname, searchParams, {\n        enhanced: false,\n        showVessel,\n        vessels\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n        columns: columns,\n        data: maintenanceChecks,\n        pageSize: 20,\n        showToolbar: false\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n        lineNumber: 713,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenanceTable, \"BtAwTu7MooJrudiBzqv5a/psZQo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData\n    ];\n});\n_c1 = MaintenanceTable;\nfunction TaskList() {\n    _s1();\n    var _s = $RefreshSig$();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__.useBreakpoints)();\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async ()=>{\n        await queryMaintenanceChecks({\n            variables: {\n                inventoryID: 0,\n                vesselID: 0\n            }\n        });\n    };\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const appendedData = activeVessels.map((item)=>({\n                ...item\n            }));\n        appendedData.push({\n            title: \"Other\",\n            id: 0\n        });\n        setVessels(appendedData);\n    };\n    const getVesselList = function(handleSetVessels) {\n        let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        _s();\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (isLoading) {\n                loadVessels();\n                setIsLoading(false);\n            }\n        }, [\n            isLoading\n        ]);\n        const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_19__.ReadVessels, {\n            fetchPolicy: \"cache-and-network\",\n            onCompleted: (queryVesselResponse)=>{\n                if (queryVesselResponse.readVessels.nodes) {\n                    handleSetVessels(queryVesselResponse.readVessels.nodes);\n                }\n            },\n            onError: (error)=>{\n                console.error(\"queryVessels error\", error);\n            }\n        });\n        const loadVessels = async ()=>{\n            if (offline) {\n                const response = await vesselModel.getAll();\n                handleSetVessels(response);\n            } else {\n                await queryVessels({\n                    variables: {\n                        limit: 200,\n                        offset: 0\n                    }\n                });\n            }\n        };\n    };\n    _s(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n        return [\n            _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery\n        ];\n    });\n    getVesselList(handleSetVessels);\n    const handleSetMaintenanceChecks = (tasks)=>{\n        setMaintenanceChecks(tasks);\n        setFilteredMaintenanceChecks(tasks);\n        const appendedData = Array.from(new Set(tasks.filter((item)=>item.assignedTo.id > 0).map((item)=>item.assignedTo.id)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        let filteredTasks = maintenanceChecks || [];\n        // Vessel filter\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.basicComponentID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.basicComponentID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.basicComponentID;\n            }\n        }\n        // Status filter\n        if (type === \"status\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.status = {\n                    in: data.map((item)=>item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.status = {\n                    eq: data.value\n                };\n            } else {\n                delete searchFilter.status;\n            }\n        }\n        // Assigned member filter\n        if (type === \"member\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.assignedToID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.assignedToID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.assignedToID;\n            }\n        }\n        // Date range\n        if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                searchFilter.expires = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete searchFilter.expires;\n            }\n        }\n        // Category\n        if (type === \"category\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.maintenanceCategoryID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.maintenanceCategoryID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.maintenanceCategoryID;\n            }\n        }\n        // Recurring filter - handle client-side filtering\n        let recurringFilter = null;\n        if (type === \"recurring\") {\n            if (data && !Array.isArray(data)) {\n                recurringFilter = data.value;\n            }\n        }\n        // Keyword filter\n        let keyFilter = keywordFilter;\n        if (type === \"keyword\" || keyFilter && keyFilter.length > 0) {\n            var _data_value;\n            const keyword = data === null || data === void 0 ? void 0 : (_data_value = data.value) === null || _data_value === void 0 ? void 0 : _data_value.trim().toLowerCase();\n            if (keyword && keyword.length > 0) {\n                filteredTasks = filteredTasks.filter((maintenanceCheck)=>[\n                        maintenanceCheck.name,\n                        maintenanceCheck.comments,\n                        maintenanceCheck.workOrderNumber\n                    ].some((field)=>field === null || field === void 0 ? void 0 : field.toLowerCase().includes(keyword)));\n                keyFilter = data.value;\n            } else {\n                keyFilter = null;\n            }\n        }\n        // Filtering based on current searchFilter\n        // Filter by vessel (basicComponentID)\n        if (searchFilter.basicComponentID) {\n            const ids = searchFilter.basicComponentID.in || [\n                searchFilter.basicComponentID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_basicComponent;\n                return ids.includes((_mc_basicComponent = mc.basicComponent) === null || _mc_basicComponent === void 0 ? void 0 : _mc_basicComponent.id);\n            });\n        }\n        // Filter by status\n        if (searchFilter.status) {\n            const statuses = searchFilter.status.in || [\n                searchFilter.status.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>statuses.includes(mc.status));\n        }\n        // Filter by assignedToID\n        if (searchFilter.assignedToID) {\n            const ids = searchFilter.assignedToID.in || [\n                searchFilter.assignedToID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>{\n                var _mc_assignedTo;\n                return ids.includes((_mc_assignedTo = mc.assignedTo) === null || _mc_assignedTo === void 0 ? void 0 : _mc_assignedTo.id);\n            });\n        }\n        // Filter by category\n        if (searchFilter.maintenanceCategoryID) {\n            const ids = searchFilter.maintenanceCategoryID.in || [\n                searchFilter.maintenanceCategoryID.eq\n            ];\n            filteredTasks = filteredTasks.filter((mc)=>ids.includes(mc.maintenanceCategoryID));\n        }\n        // Filter by date range\n        if (searchFilter.expires && searchFilter.expires.gte && searchFilter.expires.lte) {\n            filteredTasks = filteredTasks.filter((mc)=>dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isAfter(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.gte)) && dayjs__WEBPACK_IMPORTED_MODULE_5___default()(mc.startDate).isBefore(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(searchFilter.expires.lte)));\n        }\n        // Filter by recurring status\n        if (recurringFilter) {\n            if (recurringFilter === \"recurring\") {\n                // Recurring tasks have recurringID > 0\n                filteredTasks = filteredTasks.filter((mc)=>mc.recurringID > 0);\n            } else if (recurringFilter === \"one-off\") {\n                // One-off tasks have recurringID = 0 or null\n                filteredTasks = filteredTasks.filter((mc)=>!mc.recurringID || mc.recurringID === 0);\n            }\n        }\n        // Set updated filters\n        setFilter(searchFilter);\n        setKeywordFilter(keyFilter);\n        setFilteredMaintenanceChecks(filteredTasks);\n    // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);\n    };\n    const columns = createMaintenanceColumns(crewInfo || [], getVesselWithIcon, pathname, searchParams, {\n        enhanced: true,\n        showVessel: false,\n        vessels,\n        bp\n    });\n    // Row status evaluator for maintenance tasks\n    const getMaintenanceRowStatus = (maintenanceCheck)=>{\n        var _maintenanceCheck_isOverDue;\n        // Skip completed, archived, or draft tasks\n        if (maintenanceCheck.status === \"Completed\" || maintenanceCheck.archived || maintenanceCheck.status === \"Save_As_Draft\") {\n            return \"normal\";\n        }\n        const overDueStatus = (_maintenanceCheck_isOverDue = maintenanceCheck.isOverDue) === null || _maintenanceCheck_isOverDue === void 0 ? void 0 : _maintenanceCheck_isOverDue.status;\n        // Use the pre-calculated status values from the system\n        switch(overDueStatus){\n            case \"High\":\n                return \"overdue\" // Red highlighting\n                ;\n            case \"Upcoming\":\n                return \"upcoming\" // Orange highlighting\n                ;\n            case \"Medium\":\n            case \"Open\":\n            default:\n                return \"normal\" // No highlighting\n                ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_11__.ListHeader, {\n                title: \"Maintenance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_18__.SealogsMaintenanceIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1062,\n                    columnNumber: 21\n                }, void 0),\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_actions__WEBPACK_IMPORTED_MODULE_10__.MaintenanceFilterActions, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1064,\n                    columnNumber: 26\n                }, void 0),\n                titleClassName: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1059,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: maintenanceChecks && vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: filteredMaintenanceChecks || [],\n                    pageSize: 20,\n                    onChange: handleFilterOnChange,\n                    rowStatus: getMaintenanceRowStatus\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1069,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_3__.TableSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                    lineNumber: 1077,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\maintenance\\\\list\\\\list.tsx\",\n                lineNumber: 1067,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TaskList, \"3fKdCNw0/HXDN4h5aZ/KGPO1O24=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_16__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useLazyQuery\n    ];\n});\n_c2 = TaskList;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StatusBadge\");\n$RefreshReg$(_c1, \"MaintenanceTable\");\n$RefreshReg$(_c2, \"TaskList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\n"));

/***/ })

});